package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface PathToCdnFile {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    /**
     * 文件所属企业
     */
    private Long tenantId;

    /**
     * 用户ID
     * 注：如果是系统调用则传 -10000
     */
    private Long employeeId;

    /**
     * 业务线标识（必须申请注册,存在校验）
     */
    private String business;

    /**
     * 业务线子标识 (允许业务线自行分组)
     * 必须由小写字母、数字、下划线、连字符组成,长度不超过32个字符
     */
    private String businessUnit;

    /**
     * 支持N|TN|C|TC
     */
    private String path;

    /**
     * 原始文件名
     * 注：可能没有
     */
    private String name;

    /**
     * 当前仅支持 图片（jpg、webp、png、jpeg、bmp)
     */
    private String extension;

    /**
     * 文件Hash（用于幂等性校验）
     * 注：可能没有
     */
    private String hashCode;

    /**
     * 打标记（标识业务、标识一类数据、最多10个）
     * 每个标签必须由小写字母、数字、下划线、连字符组成，长度不超过20个字符
     * 最多10个标签
     * 注：可能没有
     */
    private List<String> tags;

    /**
     * 描述（描述图片的作用及内容，也可用于备注）
     * 长度不超过200个字符
     * 注：可能没有
     */
    private String description;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {
    /**
     * 仅返回文件的路径（不包括协议、域名等）
     */
    private String cdnFilePath;

    public static Result of(String cdnFilePath) {
      return new Result(cdnFilePath);
    }
  }
}
